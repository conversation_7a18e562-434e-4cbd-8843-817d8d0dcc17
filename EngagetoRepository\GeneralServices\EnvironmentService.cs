using EngagetoContracts.GeneralContracts;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;

namespace EngagetoRepository.GeneralServices
{
    public class EnvironmentService : IEnvironmentService
    {
        private readonly IConfiguration _configuration;
        private bool _isDevelopmentInitialized = false;
        private bool _isDevelopmentValue = false;

        public EnvironmentService(IConfiguration configuration)
        {
            _configuration = configuration;
            InitializeEnvironment();
        }

        public bool IsDevelopment
        {
            get
            {
                if (!_isDevelopmentInitialized)
                {
                    InitializeEnvironment();
                }
                return _isDevelopmentValue;
            }
            set
            {
                _isDevelopmentValue = value;
                _isDevelopmentInitialized = true;
            }
        }

        public HostString RequestHost { get; set; }
        public string? RequestScheme { get; set; } = string.Empty;

        private void InitializeEnvironment()
        {
            if (_isDevelopmentInitialized) return;

            // Check if running in Azure Functions environment
            var azureFunctionsEnvironment = Environment.GetEnvironmentVariable("AZURE_FUNCTIONS_ENVIRONMENT");
            var functionsWorkerRuntime = Environment.GetEnvironmentVariable("FUNCTIONS_WORKER_RUNTIME");

            if (!string.IsNullOrEmpty(functionsWorkerRuntime))
            {
                // Running in Azure Functions
                // Check for development indicators
                var isDev = azureFunctionsEnvironment?.Equals("Development", StringComparison.OrdinalIgnoreCase) == true ||
                           Environment.GetEnvironmentVariable("AzureWebJobsStorage")?.Contains("UseDevelopmentStorage=true") == true ||
                           _configuration["Environment"]?.Equals("Development", StringComparison.OrdinalIgnoreCase) == true;

                _isDevelopmentValue = isDev;
            }
            else
            {
                // Default to false for non-Function App environments (will be set by middleware)
                _isDevelopmentValue = false;
            }

            _isDevelopmentInitialized = true;
        }
        public string GetDevWebsiteLink()
        {
            return _configuration["Origins:DevWebsiteMain"];
        }
        public string GetProdWebsiteLink()
        {
            return _configuration["Origins:ProdWebsiteMain"];
        }
    }
}
