{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
    //"Storage": "DefaultEndpointsProtocol=https;AccountName=azlrbqa81ef;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    //dev database
    "ConnStr": "Data Source=engageto.database.windows.net;Initial Catalog=qa-engageto;User Id=dbmasteruser;Password=************`H9hTJK5ojM)C=$C69,2;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30;Max Pool Size=100;Min Pool Size=5;",
    // production database
    //"ConnStr": "Data Source=engageto-prd.database.windows.net;Initial Catalog=engageto-prd;User Id=dbmasteruser;Password=************`H9hTJK5ojM)C=$C69,1;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30; Max Pool Size=500;Min Pool Size=5;",
    "ServiceBusConnection": "Endpoint=sb://lrb-background-jobs.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ad6gNL9QvFB1n6EHGHp9aSrVqSznCqwEY+ASbDGg8pA=",
    "EngagetoApiUrl": "https://connect.engageto.in/api/WAWebhookMessage/receive-WAmessage"
  },
  "AWS": {
    "BucketName": "dev-engageto",
    "SecretKey": "XcDOxHtp//5J0eHMc/TL1vKL61P4imzQ4cJs/cpi",
    "AccessKey": "********************",
    "Region": "ap-south-1"
  },
  "Origins": {
    "ProdWebsiteMain": "https://app.engageto.in",
    "ProdWebsiteAlternate": "https://app.engageto.in",
    "DevWebsiteMain": "https://app.engagetod.in",
    "DevWebsiteAlternate": "https://app.engagetod.in",
    "LocalWebsite": "http://localhost:3000",
    "StaticWebsiteMain": "https://www.engageto.com",
    "StaticWebsiteAlternate": "https://engageto.com",
    "LocalWebUrl": "http://*************:3000"
  },
  "FunctionSettings": {

    "Dev_ScheduleJobUrl": "https://qa-engageto-background-app.azurewebsites.net/api/JobScheduleRequest?code=3vayGW-FSSBAAlJdk2RFbEj5f7RkvTu9cvhm3te9k-M9AzFu7ZXofw==",
    "Dev_TerminateJobUrl": "https://qa-engageto-background-app.azurewebsites.net/api/TerminatJob?code=niR6S3IMDpBTZF2omvZVn5DYiSnB3Z_AEQhl07Ap6AWhAzFu75PbXQ==",

    "Prod_ScheduleJobUrl": "https://prd-engageto-background-app.azurewebsites.net/api/JobScheduleRequest?code=RpgT8-hgFA0kz4Id8cp_eSqx2MDCIFI3muwJ3DPa5GE7AzFu6avuwQ==",
    "Prod_TerminateJobUrl": "https://prd-engageto-background-app.azurewebsites.net/api/TerminatJob?code=5KcI0jnClaKoPhUOeQ2dsHePInJoOQraD5Z-dEWWRcsYAzFujaf7Cg==",

    "Dev_ProcessSubBatchesUrl": "https://qa-engageto-background-app.azurewebsites.net/api/ProcessSubBatches?code=cL_m2hWp89UKguImsa6nzLgD7qhZtnV9b-OLKgaS6ZJEAzFujzt7vQ==",
    "Prod_ProcessSubBatchesUrl": "https://prd-engageto-background-app.azurewebsites.net/api/ProcessSubBatches?code=qs9CIdegcCDZSlbBgzpa1nlVbzI4CVQcrcjsXXLqlxLhAzFuVOnYfA=="
  }
}

