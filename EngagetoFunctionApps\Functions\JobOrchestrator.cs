﻿using Amazon.Runtime.Internal.Transform;
using EngagetoBackGroundJobs.Implementation;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.CampaignContracts;
using EngagetoEntities.Dtos.CampaignDto;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoFunctionApps.Models;
using Mapster;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using EngagetoContracts.Workflow;

namespace EngagetoFunctionApps.Functions
{
    public class JobOrchestrator
    {
        private readonly ILogger<JobOrchestrator> _logger;
        private readonly ICampaignScheduler _campaignScheduler;
        private readonly IContactScheduler _contactScheduler;
        private readonly EngagetoDapper.Data.Interfaces.GenericInterfaces.IGenericRepository _genericRepository;
        private readonly INodeWorkflowEngineService _nodeWorkflowEngineService;

        public JobOrchestrator(
            ILogger<JobOrchestrator> logger,
            ICampaignScheduler campaignScheduler,
            IContactScheduler contactScheduler,
            EngagetoDapper.Data.Interfaces.GenericInterfaces.IGenericRepository genericRepository,
            INodeWorkflowEngineService nodeWorkflowEngineService)
        {
            _logger = logger;
            _campaignScheduler = campaignScheduler;
            _contactScheduler = contactScheduler;
            _genericRepository = genericRepository;
            _nodeWorkflowEngineService = nodeWorkflowEngineService;
        }

        [Function(nameof(RunCampaignOrchestrator))]
        public async Task RunCampaignOrchestrator(
        [OrchestrationTrigger] TaskOrchestrationContext context)
        {
            var input = context.GetInput<InputPayload>();

            try
            {
                _logger.LogInformation("Orchestrator started for Id: {Id}, Input Payload: {InputPayload}", input?.Id, JsonConvert.SerializeObject(input));

                switch (input.Type)
                {
                    case "Campaign":
                        _logger.LogInformation("Calling activity function: PartitionCampaignActivity for CampaignId: {CampaignId}", input.Id);
                        await context.CallActivityAsync(nameof(PartitionCampaignActivity), input);
                        break;
                    case "Contacts":
                        _logger.LogInformation("Calling activity function: ProcessContactBulkUploadActivity for TrackerId: {TrackerId}", input.Id);
                        await context.CallActivityAsync(nameof(ProcessContactBulkUploadActivity), input);
                        break;
                    case "WorkflowJob":
                        _logger.LogInformation("Processing WorkflowJob for JobId: {JobId}", input.Id);

                        DateTime scheduledTime = input?.ScheduledTime ?? DateTime.UtcNow;
                        if (scheduledTime > context.CurrentUtcDateTime)
                        {
                            _logger.LogInformation("Scheduling WorkflowJob for later execution. JobId: {JobId}, ScheduledTime: {ScheduledTime}",
                                input.Id, scheduledTime);
                            await context.CreateTimer(scheduledTime, CancellationToken.None);
                        }
                        _logger.LogInformation("Calling activity function: ExecuteWorkflowJobActivity for JobId: {JobId}", input.Id);
                        await context.CallActivityAsync(nameof(ExecuteWorkflowJobActivity), input);
                        break;

                    default:
                        break;
                }
                _logger.LogInformation("Orchestration completed successfully for Id: {Id}", input.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred in RunCampaignOrchestrator for Id: {Id}", input?.Id);
                context.SetCustomStatus($"Error: {ex.Message}");
                throw;
            }
        }

        [Function(nameof(PartitionCampaignActivity))]
        public async Task PartitionCampaignActivity([ActivityTrigger] InputPayload input)
        {
            try
            {
                _logger.LogInformation(
                    "PartitionCampaignActivity triggered. CampaignId: {CampaignId}, ScheduledTime: {ScheduledTime}, Payload: {Payload}",
                    input.Id,
                    input.ScheduledTime,
                    JsonConvert.SerializeObject(input)
                );

                var campaign = JsonConvert.DeserializeObject<Campaign>(input.JsonData);

                await _campaignScheduler.PartitionCampaignBatchesAsync(campaign, input.IsDevelopment);

                _logger.LogInformation("PartitionCampaignActivity completed for CampaignId: {CampaignId}", input.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while processing PartitionCampaignActivity for CampaignId: {CampaignId}", input.Id);
                throw;
            }
        }


        [Function(nameof(ProcessEntityOrchestration))] // <-- new generic orchestrator name
        public async Task ProcessEntityOrchestration([OrchestrationTrigger] TaskOrchestrationContext context)
        {
            var input = context.GetInput<Payload>();

            _logger.LogInformation("ProcessEntityOrchestration started for {Type} with Id: {Id}", input?.Type, input?.Id);
            _logger.LogInformation("Input Payload: {Payload}", JsonConvert.SerializeObject(input));

            try
            {
                DateTime scheduledTime = input?.ScheduleDateTime ?? DateTime.UtcNow;

                if (scheduledTime > context.CurrentUtcDateTime)
                {
                    _logger.LogInformation("Waiting until scheduled time: {ScheduledTime}", scheduledTime);
                    await context.CreateTimer(scheduledTime, CancellationToken.None);
                }
                await context.CallActivityAsync(nameof(HandleEntityAction), input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing {Type} with Id: {Id}", input?.Type, input?.Id);
                _logger.LogInformation("Calling Fail activity.");
                throw;
            }
        }

        [Function(nameof(ProcessWorkflowJobOrchestration))]
        public async Task ProcessWorkflowJobOrchestration([OrchestrationTrigger] TaskOrchestrationContext context)
        {
            var input = context.GetInput<InputPayload>();

            _logger.LogInformation("ProcessWorkflowJobOrchestration started for JobId: {JobId}", input?.Id);
            _logger.LogInformation("Input Payload: {Payload}", JsonConvert.SerializeObject(input));

            try
            {
                DateTime scheduledTime = input?.ScheduledTime ?? DateTime.UtcNow;

                if (scheduledTime > context.CurrentUtcDateTime)
                {
                    _logger.LogInformation("WorkflowJob scheduled for later execution. JobId: {JobId}, ScheduledTime: {ScheduledTime}",
                        input.Id, scheduledTime);
                    await context.CreateTimer(scheduledTime, CancellationToken.None);
                }

                _logger.LogInformation("Executing WorkflowJob activity for JobId: {JobId}", input.Id);
                await context.CallActivityAsync(nameof(ExecuteWorkflowJobActivity), input);

                _logger.LogInformation("WorkflowJob orchestration completed successfully for JobId: {JobId}", input.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing WorkflowJob with JobId: {JobId}", input?.Id);
                context.SetCustomStatus($"Error: {ex.Message}");
                throw;
            }
        }

        [Function(nameof(ProcessContactBulkUploadActivity))]
        public async Task ProcessContactBulkUploadActivity([ActivityTrigger] InputPayload input)
        {
            try
            {
                _logger.LogInformation(
                    "ProcessContactBulkUploadActivity triggered. TrackerId: {TrackerId}, ScheduledTime: {ScheduledTime}, Payload: {Payload}",
                    input.Id,
                    input.ScheduledTime,
                    JsonConvert.SerializeObject(input)
                );
                // Deserialize the contact bulk upload data from JsonData
                var contactBulkUploadData = JsonConvert.DeserializeObject<ContactBulkUploadData>(input.JsonData);

                // Call the ContactScheduler to process the bulk upload
                await _contactScheduler.CustomerBulkUploadHandler(
                    input.Id,
                    contactBulkUploadData.BusinessId,
                    contactBulkUploadData.UserId,
                    input.Type
                );

                _logger.LogInformation("ProcessContactBulkUploadActivity completed for TrackerId: {TrackerId}", input.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while processing ProcessContactBulkUploadActivity for TrackerId: {TrackerId}", input.Id);
                throw;
            }
        }

        [Function(nameof(HandleEntityAction))]
        public async Task HandleEntityAction([ActivityTrigger] Payload input)
        {
            _logger.LogInformation("Handling Type: {Type} for Id: {Id}", input.Type ?? "null", input.Id ?? "null");

            switch (input.Type)
            {
                case "Campaign":
                    _logger.LogInformation("Input Payload: {Payload}", JsonConvert.SerializeObject(input));
                    await _campaignScheduler.ProcessCampaignBatchAsync(input.BusinessId, input.Id, JsonConvert.DeserializeObject<Campaign>(input.JsonPayload), input.Page, input.PageSize);
                    await _genericRepository.FindAndUpdateAsync(
                    "Campaigns",
                    new Dictionary<string, object> { { nameof(Campaign.State), CampaignState.Completed } },
                    new Dictionary<string, object> { { nameof(Campaign.CampaignId), Guid.Parse(input.Id) } });
                    break;
                // Add more types as needed
                default:
                    throw new InvalidOperationException($"Unsupported entity type: {input.Type}");
            }
        }

        [Function(nameof(ExecuteWorkflowJobActivity))]
        public async Task ExecuteWorkflowJobActivity([ActivityTrigger] InputPayload input)
        {
            try
            {
                _logger.LogInformation("Executing workflow job activity for JobId: {JobId}", input.Id);

                // Parse the workflow job data from JsonData
                var workflowJobData = JsonConvert.DeserializeObject<dynamic>(input.JsonData);
                string methodName = workflowJobData.MethodName;
                var parameters = workflowJobData.Parameters;

                switch (methodName)
                {
                    case "ExecuteUnifiedReminderTemplateJob":
                        await _nodeWorkflowEngineService.ExecuteUnifiedReminderTemplateJob(
                            (Guid)parameters.contactId,
                            parameters.templateJson.ToString());
                        break;

                    case "ExecuteTimeoutJobAsync":
                        await _nodeWorkflowEngineService.ExecuteTimeoutJobAsync(
                            (Guid)parameters.contactId,
                            (Guid)parameters.nodeId,
                            parameters.nodeType.ToString());
                        break;

                    default:
                        throw new InvalidOperationException($"Unsupported workflow job method: {methodName}");
                }

                _logger.LogInformation("Workflow job activity completed for method: {MethodName}", methodName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while executing workflow job activity for JobId: {JobId}", input.Id);
                throw;
            }
        }
    }
}
